import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { db } from '@/prisma/db';

export const maxDuration = 60;

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const token = await getToken({ req: request });
    const { llm_provider, video_avatar } = await request.json();
    const { id } = params;

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!id) {
      return NextResponse.json(
        { error: 'Interview ID is required' },
        { status: 400 }
      );
    }

    // Validate llm_provider values
    const validProviders = ['openai', 'google', 'anthropic'];
    if (llm_provider && !validProviders.includes(llm_provider)) {
      return NextResponse.json(
        { error: 'Invalid LLM provider. Must be one of: openai, google, anthropic' },
        { status: 400 }
      );
    }

    // Validate video_avatar is boolean
    if (video_avatar !== undefined && typeof video_avatar !== 'boolean') {
      return NextResponse.json(
        { error: 'video_avatar must be a boolean value' },
        { status: 400 }
      );
    }

    // First, check if the interview exists
    const interview = await db.careerPractice.findFirst({
      where: {
        id,
      },
      include: {
        eventDetails: {
          select: {
            organizationId: true,
            name: true,
            isAiQuestion: true,
          },
        },
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    console.log('Interview lookup for AI settings:', { id, found: !!interview, userId: token.sub });

    if (!interview) {
      return NextResponse.json(
        { error: 'Interview not found', debug: { id, userId: token.sub } },
        { status: 404 }
      );
    }

    // Check if this is an AI interview
    if (!interview.eventDetails?.isAiQuestion) {
      return NextResponse.json(
        { error: 'AI settings can only be configured for AI interviews' },
        { status: 400 }
      );
    }

    // Check if interview is completed
    if (interview.interviewStatus === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Cannot modify AI settings for completed interviews' },
        { status: 400 }
      );
    }

    // Get existing comments or initialize empty object
    const existingComments = interview.comments || {};

    // Update the comments with AI settings
    const updatedComments = {
      ...existingComments,
      llm_provider: llm_provider || 'openai',
      video_avatar: video_avatar !== undefined ? video_avatar : false,
    };

    // Update the interview with AI settings in comments
    const updatedInterview = await db.careerPractice.update({
      where: { id },
      data: {
        comments: updatedComments,
      },
      select: {
        id: true,
        comments: true,
        interviewStatus: true,
        user: {
          select: {
            name: true,
            email: true,
          },
        },
        eventDetails: {
          select: {
            name: true,
            round: true,
            isAiQuestion: true,
          },
        },
      },
    });

    return NextResponse.json(
      {
        message: 'AI settings updated successfully',
        interview: updatedInterview,
        settings: {
          llm_provider: updatedComments.llm_provider,
          video_avatar: updatedComments.video_avatar,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating AI settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
