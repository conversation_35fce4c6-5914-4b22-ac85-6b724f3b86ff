'use client';

import React, { useState } from 'react';

import { useRouter } from 'next/navigation';

import { EdgeCaseCard } from '@/components/cards/edge-case';
import { ReScheduleInterviewModal } from '@/components/interview/interview-reschedule-Modal';
import { RejectModal } from '@/components/interview/rejectModal';
import { CombinedHeroMetrics } from '@/components/section/combined-hero-metrics';
import { ModernDetailedAnalysis } from '@/components/section/modern-detailed-analysis';
import { InterviewTimeline } from '@/components/ui/interview-timeline';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useHandleGenerateFeedback } from '@/hooks/mutation/use-handle-generate-feedback';
import { useHandleReject } from '@/hooks/mutation/use-handle-reject';
import { useHandleResend } from '@/hooks/mutation/use-handle-resend';
import { useHandleScheduleCall } from '@/hooks/mutation/use-handle-schedule-call';
import { updateCareerPractice } from '@/services/apicall';
import { timeAgo } from '@/utils/time-ago';
import { MessageSquare, Send } from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import { Button } from '@camped-ui/button';
import { Textarea } from '@camped-ui/textarea';

import PageHeader from '../../page-header';
import { VideoInterviewDetailScreen } from './video-interview-detail-screen';
import { CandidateOtherInterviews } from '../../interview/candidate-other-interviews';
import { InterviewerQuestionsButton } from '../../interview/interviewer-questions-button';
import { AIInterviewSettingsButton } from '../../interview/ai-interview-settings-button';

export const ViewInterviewDetailScreen = ({
  careerPractice,
  userProfile,
  user,
  userId,
  members,
  isPublic = false,
  platform,
  candidateId,
  organizationId,
  currentOrgId,
  currentOrgRole,
}) => {
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showReScheduleModal, setShowReScheduleModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [loading, setLoading] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [flag, setFlag] = useState(careerPractice?.feedback?.overall_recommendation?.decision);
  const [reason, setReason] = useState(careerPractice?.feedback?.overall_recommendation?.reason);
  const [comment, setComment] = useState(careerPractice?.interviewerComments ?? []);
  const router = useRouter();
  const handleOnSave = async () => {
    toast.loading('Saving changes');
    const update = await updateCareerPractice({
      id: careerPractice?.id,
      data: {
        feedback: {
          ...careerPractice?.feedback,
          overall_recommendation: {
            reason,
            decision: flag,
          },
        },
      },
    });

    toast.remove();
    if (update?.careerPractice?.id) {
      toast.success('Changes saved');
      router.refresh();
    } else {
      toast.error('Failed to save changes');
    }
  };
  const multipleChoiceQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'multiple-choice',
  );
  const videoQuestion = careerPractice?.conversation?.filter(
    (item) => item.round === 'video-interview',
  );
  const codingQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'coding-interview',
  );
  const frontendQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'frontend-interview',
  );
  const writtenQuestions = careerPractice?.conversation?.filter(
    (item) => item.round === 'written-interview',
  );

  const { mutate: handleResend } = useHandleResend({ eventId: careerPractice?.eventId });
  const mergeAllQuestions = () => {
    let questions = [...videoQuestion, ...codingQuestions, ...frontendQuestions] as any;

    if (multipleChoiceQuestions?.length) {
      questions = [...questions, multipleChoiceQuestions];
    }
    if (writtenQuestions?.length) {
      questions = [...questions, writtenQuestions];
    }

    return {
      questions,
    };
  };

  const availableRound = () => {
    let availableRound: any = [];
    if (videoQuestion?.length > 0) {
      availableRound.push('Video Round');
    }
    if (codingQuestions?.length > 0 || frontendQuestions?.length > 0) {
      availableRound.push('Coding Round');
    }
    if (multipleChoiceQuestions?.length > 0) {
      availableRound.push('MCQs');
    }
    if (writtenQuestions?.length > 0) {
      availableRound.push('Written');
    }
    return availableRound;
  };
  const { questions }: { questions: any } = mergeAllQuestions();

  const form = useForm({
    defaultValues: {
      status: careerPractice?.comments?.[0]?.status ?? 'Rejected',
      feedback:
        careerPractice?.comments?.[0]?.message ??
        'Thank you for your application and the time you invested in interviewing with us. We regret to inform you that we will not be proceeding with your candidacy for this position. We encourage you to apply for future opportunities as they become available.',
      meetingType: careerPractice?.VideoCallInterview[0]?.meetingType ?? 'videoCall',
      meetingDateTime: new Date(
        careerPractice?.VideoCallInterview[0]?.scheduleTime ?? new Date(),
      ).toISOString(),
      selectedDate: new Date(
        careerPractice?.VideoCallInterview[0]?.scheduleTime ?? new Date(),
      ).toISOString(),
      interviewer:
        members?.items?.filter(
          (member) =>
            careerPractice?.VideoCallInterview[0]?.interviewerId?.includes(member?.userId),
        ) ?? null,
      address: careerPractice?.VideoCallInterview[0]?.address,
      meetingDuration: careerPractice?.VideoCallInterview[0]?.interviewDuration ?? '60',
    },
  });
  const { mutate: handleReject } = useHandleReject({
    showRejectModal,
    setSelectedCandidate: () => {},
    setShowRejectModal,
    userId,
    candidateStatus: form.watch('status'),
    candidateFeedback: form.watch('feedback'),
    setIsLoading,
  });
  const { mutate: handleSchedule } = useHandleScheduleCall({
    form,
    careerPracticeId: careerPractice?.id,
    setShowRejectModal,
    platform,
  });
  const { mutate: handleReSchedule } = useHandleScheduleCall({
    form,
    careerPracticeId: careerPractice?.id,
    setShowRejectModal: setShowReScheduleModal,
    platform,
    isUpdate: true,
  });
  const { mutate: handleGenerateFeedback } = useHandleGenerateFeedback({
    careerPractice,
    round: careerPractice?.isPlacement ? 'Interviewathon' : 'Interview',
  });

  const handleSingleCandidateSubmit = async (withLink: boolean, sendEmail: boolean, aiEventId?: string) => {
    const status = form.getValues('status');

    // Handle Move to AI Round separately
    if (status === 'Move to AI Round' && aiEventId) {
      try {
        toast.loading('Moving candidate to AI round...');

        const response = await fetch('/api/admin/move-to-ai-round', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            candidateIds: [careerPractice?.id],
            aiEventId,
            organizationId: organizationId,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to move candidate to AI round');
        }

        toast.dismiss();
        toast.success(result.message);

        // Close modal and refresh
        setShowRejectModal(false);
        window.location.reload(); // Use window.location.reload() since router might not be available
      } catch (error) {
        toast.dismiss();
        toast.error(error.message || 'Failed to move candidate to AI round');
      }
      return;
    }

    // Handle regular feedback submission
    handleReject({ id: careerPractice?.id, withLink, sendEmail });
  };

  const handleAddComment = async () => {
    setLoading(true);
    toast.loading('Adding note..');
    const update = await updateCareerPractice({
      id: careerPractice?.id,
      data: {
        interviewerComments: [
          ...(comment?.length > 0
            ? comment?.map((item) => ({ ...item, commentBy: item?.commentBy?.id }))
            : []),
          { comment: newComment, commentBy: userId, date: new Date() },
        ],
      },
    });
    toast.remove();
    setLoading(false);
    if (update?.careerPractice) {
      toast.success(`Note added`);
      setComment(update?.careerPractice?.interviewerComments);
      setNewComment('');
    } else {
      toast.error(`Failed to add note`);
    }
  };

  const disableFeedback = ['Move to next round', 'Rejected', 'Accepted']?.includes(
    careerPractice?.comments?.[0]?.status,
  );

  return (
    <>
      {!isPublic && (
        <PageHeader
          title={`Performance Analysis - ${
            userProfile?.fullName === '' || !userProfile?.fullName
              ? user?.email
              : userProfile?.fullName
          }`}
          hasBack
        />
      )}

      {/* Interviewer Questions Section */}
      {!isPublic && (
        <div className="mb-4 flex items-center justify-between rounded-lg border bg-white p-4 shadow-sm">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <span className="font-medium">Interviewer Questions</span>
            </div>
            {careerPractice?.eventDetails?.round && (
              <div className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
                {careerPractice.eventDetails.round.charAt(0).toUpperCase() + careerPractice.eventDetails.round.slice(1)} Round
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <InterviewerQuestionsButton
              candidateId={candidateId}
              interviewId={careerPractice?.id}
              candidateName={userProfile?.fullName || user?.email || 'Candidate'}
              interviewName={careerPractice?.eventDetails?.name || careerPractice?.event || 'Interview'}
              roundType={careerPractice?.eventDetails?.round}
              hasQuestions={!!(careerPractice?.interviewerQuestions && careerPractice?.interviewerQuestions.trim())}
              currentQuestions={careerPractice?.interviewerQuestions || ''}
              interviewStatus={careerPractice?.interviewStatus || 'NOT_STARTED'}
            />
            {(() => {
              console.log('ViewInterviewDetailScreen: careerPractice data for AI Settings', {
                isAiQuestion: careerPractice?.eventDetails?.isAiQuestion,
                eventDetails: careerPractice?.eventDetails,
                comments: careerPractice?.comments,
                interviewStatus: careerPractice?.interviewStatus
              });
              return null;
            })()}
            <AIInterviewSettingsButton
              candidateId={candidateId}
              interviewId={careerPractice?.id}
              candidateName={userProfile?.fullName || user?.email || 'Candidate'}
              interviewName={careerPractice?.eventDetails?.name || careerPractice?.event || 'Interview'}
              roundType={careerPractice?.eventDetails?.round}
              currentLlmProvider={careerPractice?.comments?.llm_provider}
              currentVideoAvatar={careerPractice?.comments?.video_avatar}
              interviewStatus={careerPractice?.interviewStatus || 'NOT_STARTED'}
              isAiInterview={careerPractice?.eventDetails?.isAiQuestion || false}
            />
          </div>
        </div>
      )}

      <div className="mt-2 flex w-full flex-col">
        <div className="flex w-full flex-col gap-4">
          {!careerPractice?.conversation?.[0]?.room_name ? (
            <Tabs defaultValue={`overall`} className="w-full">
              <TabsList className="flex h-12 w-full items-start justify-start gap-2 overflow-x-auto">
                <TabsTrigger value="overall" className="flex h-10 items-center justify-center">
                  Overview
                </TabsTrigger>
                <TabsTrigger value="notes" className="flex h-10 items-center justify-center">
                  Notes
                </TabsTrigger>
                <TabsTrigger value="recordings" className="flex h-10 items-center justify-center">
                  Interview Recordings
                </TabsTrigger>
              </TabsList>
              <TabsContent value="overall" className="flex w-full flex-col gap-8">
                {/* Combined Hero and Performance Metrics */}
                <CombinedHeroMetrics
                  careerPractice={careerPractice}
                  userProfile={userProfile}
                  onSendFeedback={handleReject}
                  disableFeedback={disableFeedback}
                  isLoading={isLoading}
                  isPublic={isPublic}
                  loading={loading}
                  onGenerateFeedback={handleGenerateFeedback}
                  onInviteCandidate={handleResend}
                />
                <InterviewTimeline careerPractice={careerPractice} />
                {!careerPractice?.timing?.completedTime && !careerPractice?.feedback?.resume && (
                  <EdgeCaseCard
                    title="No Feedback Available"
                    description="Once the candidate has completed the interview, the feedback will be available."
                  />
                )}

                {/* Collapsible Detailed Analysis Section */}
                <div className="space-y-6">
                  <ModernDetailedAnalysis
                    careerPractice={careerPractice}
                    userProfile={userProfile}
                    questions={questions}
                    userId={userId}
                    reason={reason}
                    flag={flag}
                    setFlag={setFlag}
                    setReason={setReason}
                    handleOnSave={handleOnSave}
                  />
                </div>
              </TabsContent>
              <TabsContent value="recordings" className="flex w-full flex-col gap-4">
                {careerPractice?.videoRecordings?.screen?.length === 0 &&
                  careerPractice?.videoRecordings?.webcam?.length === 0 && (
                    <EdgeCaseCard
                      title="No Recordings Available"
                      description="Currently no recordings available."
                    />
                  )}
                {careerPractice?.videoRecordings?.screen?.map((recording: any, index: number) => (
                  <video
                    id="screen-remote-video"
                    key={index}
                    className="rounded-md"
                    src={recording}
                    controls
                    playsInline
                    autoPlay={true}
                    muted={false}
                    width="550" // You can adjust the size
                    height="300"
                  />
                ))}
                {careerPractice?.videoRecordings?.webcam?.map((recording: any, index: number) => (
                  <video
                    id="screen-remote-video"
                    className="rounded-md"
                    src={recording}
                    key={index}
                    controls
                    playsInline
                    autoPlay={true}
                    muted={false}
                    width="550" // You can adjust the size
                    height="300"
                  />
                ))}
              </TabsContent>

              {/* Notes Tab */}
              <TabsContent value="notes" className="flex w-full flex-col gap-6">
                <div className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800">
                  <div className="mb-6 flex items-center gap-4">
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                      <MessageSquare className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Interview Notes
                      </h2>
                      <p className="text-gray-600 dark:text-gray-300">
                        Interviewer comments and observations
                      </p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div className="max-h-[500px] space-y-4 overflow-y-auto">
                      {comment.length === 0 ? (
                        <div className="py-12 text-center">
                          <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                          <p className="text-lg text-gray-500 dark:text-gray-400">No notes yet</p>
                          <p className="text-gray-400 dark:text-gray-500">
                            Add the first note below to get started
                          </p>
                        </div>
                      ) : (
                        comment.map((note, index) => (
                          <div key={index} className="rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
                            <div className="mb-3 flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
                                  <span className="text-sm font-medium text-blue-600">
                                    {(note?.commentBy?.userProfile?.fullName ||
                                      note?.commentBy?.email ||
                                      'U')[0].toUpperCase()}
                                  </span>
                                </div>
                                <span className="font-medium text-gray-900 dark:text-gray-100">
                                  {note?.commentBy?.userProfile?.fullName || note?.commentBy?.email}
                                </span>
                              </div>
                              <span className="text-sm text-gray-500">{timeAgo?.(note?.date)}</span>
                            </div>
                            <p className="leading-relaxed text-gray-900 dark:text-gray-100">
                              {note?.comment}
                            </p>
                          </div>
                        ))
                      )}
                    </div>

                    <div className="border-t border-gray-200 pt-6 dark:border-gray-700">
                      <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Add New Note
                      </label>
                      <Textarea
                        placeholder="Write your observations, feedback, or notes about the candidate..."
                        value={newComment}
                        onChange={(e) => setNewComment?.(e.target.value)}
                        className="mb-4 min-h-[120px] resize-none"
                      />
                      <Button
                        className="w-full"
                        disabled={loading || !newComment.trim()}
                        onClick={handleAddComment}
                      >
                        <Send className="mr-2 h-4 w-4" />
                        {loading ? 'Adding Note...' : 'Add Note'}
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          ) : (
            <>
              {/* Interviewer Questions Section for AI Interviews */}
              {!isPublic && (
                <div className="mb-4 flex items-center justify-between rounded-lg border bg-white p-4 shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">Interviewer Questions</span>
                    </div>
                    {careerPractice?.eventDetails?.round && (
                      <div className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
                        {careerPractice.eventDetails.round.charAt(0).toUpperCase() + careerPractice.eventDetails.round.slice(1)} Round
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <InterviewerQuestionsButton
                      candidateId={candidateId}
                      interviewId={careerPractice?.id}
                      candidateName={userProfile?.fullName || user?.email || 'Candidate'}
                      interviewName={careerPractice?.eventDetails?.name || careerPractice?.event || 'Interview'}
                      roundType={careerPractice?.eventDetails?.round}
                      hasQuestions={!!(careerPractice?.interviewerQuestions && careerPractice?.interviewerQuestions.trim())}
                      currentQuestions={careerPractice?.interviewerQuestions || ''}
                      interviewStatus={careerPractice?.interviewStatus || 'NOT_STARTED'}
                    />
                    <AIInterviewSettingsButton
                      candidateId={candidateId}
                      interviewId={careerPractice?.id}
                      candidateName={userProfile?.fullName || user?.email || 'Candidate'}
                      interviewName={careerPractice?.eventDetails?.name || careerPractice?.event || 'Interview'}
                      roundType={careerPractice?.eventDetails?.round}
                      currentLlmProvider={careerPractice?.comments?.llm_provider}
                      currentVideoAvatar={careerPractice?.comments?.video_avatar}
                      interviewStatus={careerPractice?.interviewStatus || 'NOT_STARTED'}
                      isAiInterview={careerPractice?.eventDetails?.isAiQuestion || false}
                    />
                  </div>
                </div>
              )}

              <VideoInterviewDetailScreen
                meeting={{ ...careerPractice, s3RecordingId: careerPractice.conversation?.[0]?.s3Id }}
                userId={userId}
                resumeUrl={userProfile?.resumeUrl}
                isAiInterview={true}
              />
            </>
          )}
        </div>
      </div>

      {/* Other Interviews Right Panel */}
      {candidateId && organizationId && !isPublic && (
        <CandidateOtherInterviews
          candidateId={candidateId}
          organizationId={organizationId}
          currentInterviewId={careerPractice?.id}
          currentOrgId={currentOrgId}
          currentOrgRole={currentOrgRole}
        />
      )}

      <RejectModal
        form={form}
        members={members}
        handleSubmit={(withLink: boolean, sendEmail: boolean, aiEventId?: string) =>
          handleSingleCandidateSubmit(withLink, sendEmail, aiEventId)
        }
        setShowPopup={setShowRejectModal}
        showPopup={showRejectModal}
        handleSchedule={handleSchedule}
        platform={platform}
      />
      <ReScheduleInterviewModal
        form={form}
        members={members}
        setShowPopup={setShowReScheduleModal}
        showPopup={showReScheduleModal}
        handleSchedule={handleReSchedule}
        platform={platform}
      />
    </>
  );
};
