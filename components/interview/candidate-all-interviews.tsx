'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';

// Import the InterviewerQuestionsButton component
import { InterviewerQuestionsButton } from './interviewer-questions-button';
import { AIInterviewSettingsButton } from './ai-interview-settings-button';
import { Calendar, Clock, FileText, User, ArrowRight, Briefcase, Video, ExternalLink } from 'lucide-react';
import { timeAgo } from '@/utils/time-ago';

interface Interview {
  id: string;
  role: string;
  level: string;
  event: string;
  finalScore?: number;
  resumeScore?: number;
  interviewStatus: string;
  completedTime?: string;
  createdAt: string;
  scheduleTime?: string;
  feedback?: any;
  comments?: {
    llm_provider?: string;
    video_avatar?: boolean;
    [key: string]: any;
  };
  interviewerQuestions?: string;
  user?: {
    name: string;
    email: string;
  };
  eventDetails?: {
    id: string;
    name: string;
    role: string;
    level: string;
    isAiQuestion: boolean;
    isPlacement: boolean;
    round?: string;
  };
  type: 'career_practice' | 'video_meeting';
  interviewType: string;
  meetingType?: string;
  careerPracticeId?: string;
}

interface CandidateAllInterviewsProps {
  candidateId: string;
  organizationId: string;
  currentOrgId?: string;
  currentOrgRole?: string;
}

export const CandidateAllInterviews: React.FC<CandidateAllInterviewsProps> = ({
  candidateId,
  organizationId,
  currentOrgId,
  currentOrgRole,
}) => {
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAllInterviews = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          candidateId,
          organizationId,
          currentInterviewId: '', // No current interview to exclude
          ...(currentOrgId && { membershipId: currentOrgId }),
          ...(currentOrgRole && { role: currentOrgRole }),
        });

        const response = await fetch(`/api/admin/get-candidate-other-interviews?${params}`);
        const data = await response.json();

        if (response.ok) {
          setInterviews(data.interviews || []);
        } else {
          setError(data.message || 'Failed to fetch interviews');
        }
      } catch (err) {
        setError('Error loading interviews');
        console.error('Error fetching interviews:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchAllInterviews();
  }, [candidateId, organizationId, currentOrgId, currentOrgRole]);

  const getStatusBadge = (status: string, recommendation?: string, type?: string) => {
    if (type === 'video_meeting') {
      if (status === 'COMPLETED') {
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      } else if (status === 'PENDING') {
        return <Badge className="bg-yellow-100 text-yellow-800">Scheduled</Badge>;
      } else if (status === 'CANCELLED') {
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      }
      return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }

    if (status === 'COMPLETED') {
      if (recommendation === 'shortlisted') {
        return <Badge className="bg-green-100 text-green-800">Shortlisted</Badge>;
      } else if (recommendation === 'rejected') {
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      }
      return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>;
    } else if (status === 'PARTIALLY_COMPLETED') {
      return <Badge className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
    }
    return <Badge className="bg-gray-100 text-gray-800">Not Started</Badge>;
  };

  const getInterviewTypeIcon = (interview: Interview) => {
    if (interview.type === 'video_meeting') {
      return <Video className="h-5 w-5 text-green-600" />;
    } else if (interview.eventDetails?.isAiQuestion) {
      return <User className="h-5 w-5 text-purple-600" />;
    } else if (interview.eventDetails?.isPlacement) {
      return <Briefcase className="h-5 w-5 text-blue-600" />;
    }
    return <FileText className="h-5 w-5 text-gray-600" />;
  };

  const getInterviewLink = (interview: Interview) => {
    if (interview.type === 'video_meeting') {
      return `/video-interviews/${interview.id}`;
    } else if (interview.careerPracticeId) {
      return `/interviews/${interview.eventDetails?.id}/${interview.careerPracticeId}`;
    } else {
      return `/interviews/${interview.eventDetails?.id}/${interview.id}`;
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">All Interviews & Meetings</h2>
          <div className="text-sm text-gray-500">Loading...</div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">All Interviews & Meetings</h2>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="text-red-500 mb-2">Error loading interviews</div>
              <div className="text-sm text-gray-500">{error}</div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (interviews.length === 0) {
    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">All Interviews & Meetings</h2>
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <div className="text-gray-500 mb-2">No interviews or meetings found</div>
              <div className="text-sm text-gray-400">
                This candidate hasn't taken any interviews or scheduled meetings in this organization
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">All Interviews & Meetings</h2>
        <Badge variant="secondary" className="text-sm">
          {interviews.length} total
        </Badge>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {interviews.map((interview) => (
          <Card key={interview.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {getInterviewTypeIcon(interview)}
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-base truncate">
                      {interview.eventDetails?.name || interview.event}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {interview.eventDetails?.role || interview.role} • {interview.eventDetails?.level || interview.level}
                    </CardDescription>
                  </div>
                </div>
                <Link href={getInterviewLink(interview)} target="_blank" rel="noopener noreferrer">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="space-y-3">
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {timeAgo(interview.createdAt)}
                  </div>
                  {interview.completedTime && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      Completed
                    </div>
                  )}
                </div>
                
                {(interview.finalScore || interview.resumeScore) && (
                  <div className="text-sm font-medium">
                    Score: {Math.round(interview.finalScore || interview.resumeScore || 0)}
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusBadge(
                      interview.interviewStatus,
                      interview.feedback?.overall_recommendation?.decision,
                      interview.type
                    )}
                    {interview.type === 'video_meeting' && (
                      <Badge variant="outline" className="text-xs text-green-600 border-green-200">
                        Video
                      </Badge>
                    )}
                    {interview.eventDetails?.isAiQuestion && (
                      <Badge variant="outline" className="text-xs text-purple-600 border-purple-200">
                        AI
                      </Badge>
                    )}
                    {interview.eventDetails?.isPlacement && (
                      <Badge variant="outline" className="text-xs text-blue-600 border-blue-200">
                        Placement
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <InterviewerQuestionsButton
                      candidateId={candidateId}
                      interviewId={interview.id}
                      candidateName={interview.user?.name || 'Candidate'}
                      interviewName={interview.eventDetails?.name || interview.event}
                      roundType={interview.eventDetails?.round}
                      hasQuestions={!!(interview.interviewerQuestions && interview.interviewerQuestions.trim())}
                      currentQuestions={interview.interviewerQuestions}
                      interviewStatus={interview.interviewStatus || 'NOT_STARTED'}
                    />
                    <AIInterviewSettingsButton
                      candidateId={candidateId}
                      interviewId={interview.id}
                      candidateName={interview.user?.name || 'Candidate'}
                      interviewName={interview.eventDetails?.name || interview.event}
                      roundType={interview.eventDetails?.round}
                      currentLlmProvider={interview.comments?.llm_provider}
                      currentVideoAvatar={interview.comments?.video_avatar}
                      interviewStatus={interview.interviewStatus || 'NOT_STARTED'}
                      isAiInterview={interview.eventDetails?.isAiQuestion || false}
                    />
                    <Link href={getInterviewLink(interview)} target="_blank" rel="noopener noreferrer">
                      <Button variant="outline" size="sm" className="text-xs">
                        View Details
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
