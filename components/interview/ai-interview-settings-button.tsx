'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { Icon } from '@/icons';

import { Button } from '@camped-ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@camped-ui/dialog';
import { Badge } from '@camped-ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@camped-ui/select';
import { Switch } from '@camped-ui/switch';
import { Label } from '@camped-ui/label';

interface AIInterviewSettingsButtonProps {
  candidateId: string;
  interviewId: string;
  candidateName: string;
  interviewName: string;
  roundType?: string;
  currentLlmProvider?: string;
  currentVideoAvatar?: boolean;
  interviewStatus: string;
  isAiInterview: boolean;
}

export const AIInterviewSettingsButton = ({
  candidateId,
  interviewId,
  candidateName,
  interviewName,
  roundType,
  currentLlmProvider = 'openai',
  currentVideoAvatar = false,
  interviewStatus,
  isAiInterview
}: AIInterviewSettingsButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [llmProvider, setLlmProvider] = useState(currentLlmProvider);
  const [videoAvatar, setVideoAvatar] = useState(currentVideoAvatar);
  const [isLoading, setIsLoading] = useState(false);

  const isCompleted = interviewStatus === 'COMPLETED';

  console.log('AI Settings Button: Debug info', {
    isAiInterview,
    currentLlmProvider,
    currentVideoAvatar,
    interviewStatus,
    interviewId,
    candidateName
  });

  // Only show for AI interviews
  if (!isAiInterview) {
    console.log('AI Settings Button: Not showing because isAiInterview is false');
    // Temporarily show a debug button to see if the component is being rendered
    return (
      <Button
        variant="outline"
        size="sm"
        className="text-xs flex items-center gap-1 bg-red-100"
        disabled
      >
        <Icon name="Settings" className="h-3 w-3" />
        Debug: Not AI
      </Button>
    );
  }

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/interview/${interviewId}/update-ai-settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          llm_provider: llmProvider,
          video_avatar: videoAvatar,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update AI settings');
      }

      toast.success('AI settings updated successfully');
      setIsOpen(false);
      // Refresh the page to show updated data
      window.location.reload();
    } catch (error) {
      console.error('Error updating AI settings:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update AI settings');
    } finally {
      setIsLoading(false);
    }
  };

  const hasCustomSettings = currentLlmProvider !== 'openai' || currentVideoAvatar !== false;

  if (isCompleted) {
    return hasCustomSettings ? (
      <Badge variant="secondary" className="text-xs flex items-center gap-1">
        <Icon name="Settings" className="h-3 w-3" />
        AI Settings
      </Badge>
    ) : null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={hasCustomSettings ? "default" : "outline"} 
          size="sm"
          className="text-xs flex items-center gap-1"
        >
          <Icon name="Settings" className="h-3 w-3" />
          {hasCustomSettings ? 'AI Settings' : 'Configure AI'}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Icon name="Settings" className="h-5 w-5" />
            AI Settings for {candidateName}
          </DialogTitle>
          <div className="text-sm text-gray-600 space-y-1">
            <p>Interview: {interviewName}</p>
            {roundType && (
              <Badge variant="outline" className="text-xs">
                {roundType.charAt(0).toUpperCase() + roundType.slice(1)} Round
              </Badge>
            )}
          </div>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              LLM Provider
            </Label>
            <Select value={llmProvider} onValueChange={setLlmProvider}>
              <SelectTrigger>
                <SelectValue placeholder="Select LLM Provider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="google">Google</SelectItem>
                <SelectItem value="anthropic">Anthropic</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500">
              Choose the AI model provider for this candidate's interview
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">
                Video Avatar
              </Label>
              <Switch
                checked={videoAvatar}
                onCheckedChange={setVideoAvatar}
              />
            </div>
            <p className="text-xs text-gray-500">
              Enable video avatar for a more interactive interview experience
            </p>
          </div>

          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-500">
              These settings are specific to this candidate
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setIsOpen(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSave}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Icon name="Loader2" className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Settings'
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
