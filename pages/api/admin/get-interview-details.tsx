import { getSignedUrl } from '@/pages/api/gcp-bucket';
import { db } from '@/prisma/db';

import { listFolderContents } from '../upload-url';

const baseUrl = process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL;
const handler = async (req: any, res: any) => {
  const { id, membershipId, role } = req.query;
  let where = {};
  if (membershipId && !['admin', 'owner']?.includes(role?.toLowerCase())) {
    where = {
      eventDetails: {
        MembershipOnEventDetails: {
          some: {
            membershipId,
          },
        },
      },
    };
  }

  const careerPractice = await db.careerPractice.findFirst({
    include: {
      user: {
        select: {
          email: true,
          userProfile: true,
        },
      },
      VideoCallInterview: true,
      eventDetails: {
        select: {
          isAiQuestion: true,
          isPlacement: true,
          organizationId: true,
          name: true,
          round: true,
        },
      },
    },
    where: {
      id: id,
      ...where,
    },
  });

  if (!careerPractice) {
    return res.status(200).json({
      result: [],
      error: 'Result Not Found',
    });
  }

  const userResponse = careerPractice?.user;
  const response = careerPractice?.user?.userProfile;

  // Assuming careerPractice is your data structure containing conversation
  const conversation = careerPractice?.conversation;

  // Use Promise.all to concurrently fetch the pre-signed URLs
  const mappedResponses = await Promise.all(
    (Array.isArray(conversation) ? conversation : [])?.map(async (response) => {
      try {
        if (
          typeof response === 'object' &&
          response !== null &&
          's3Id' in response &&
          response.s3Id
        ) {
          let videoUrl;
          if (response?.videoOrigin === 'GCP_BUCKET') {
            videoUrl = await getSignedUrl(
              response?.s3Id,
              careerPractice?.eventDetails?.organizationId,
            );
          } else {
            videoUrl = `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/${response.s3Id}`;
          }
          return {
            ...response,
            videoUrl,
          };
        } else {
          // Handle cases where response or response.s3Id is missing or falsy.
          // You can choose to return some default value or handle the error accordingly.
          if (typeof response === 'object' && response !== null) {
            return {
              ...response,
              videoUrl: null, // or any default value
            };
          } else {
            return {
              videoUrl: null,
            };
          }
        }
      } catch (error) {
        // Handle errors that occur while fetching the pre-signed URL.
        // You can choose to throw the error, log it, or handle it as needed.
        console.error(`Error fetching pre-signed URL for response:`, error);
        throw error; // Optionally rethrow the error if needed.
      }
    }),
  );
  const Video_recordings: any = await listFolderContents(id);
  const separatedVideos: any = {
    screen: [],
    webcam: [],
  };

  Video_recordings?.Contents?.forEach((recording) => {
    if (recording.Key.includes('screen')) {
      separatedVideos.screen.push(`${baseUrl}/${recording.Key}`);
    } else if (recording.Key.includes('webcam')) {
      separatedVideos.webcam.push(`${baseUrl}/${recording.Key}`);
    } else if (recording.Key.includes('composite')) {
      // Composite videos contain both screen and webcam, show in screen section
      separatedVideos.screen.push(`${baseUrl}/${recording.Key}`);
    }
  });

  let comments: any = careerPractice?.interviewerComments ?? [];
  if (comments?.length > 0) {
    let commentByIds = comments?.map((comment) => comment?.commentBy);
    commentByIds = commentByIds?.filter((item) => item);
    const commentByUsers: any = await db.user.findMany({
      where: {
        id: { in: commentByIds },
      },
      include: {
        userProfile: {
          select: { fullName: true },
        },
      },
    });
    comments = comments?.map((comment) => {
      return {
        ...comment,
        commentBy: commentByUsers.find((user) => user?.id === comment?.commentBy),
      };
    });
  }

  if (response?.resumeS3Id) {
    const url = `${process.env.NEXT_PUBLIC_ACEPREP_CLOUDFRONT_URL}/${process.env.NEXT_PUBLIC_S3FOLDER}/resume/${response?.resumeS3Id}`;

    return res.status(200).json({
      items: {
        result: {
          ...careerPractice,
          conversation: mappedResponses,
          isPlacement: careerPractice?.eventDetails?.isPlacement,
          interviewerComments: comments,
          videoRecordings: separatedVideos,
        },
        userProfile: { ...response, resumeUrl: url },
        user: userResponse,
      },
    });
  }

  return res.status(200).json({
    items: {
      result: {
        ...careerPractice,
        isAiQuestion: careerPractice?.eventDetails?.isAiQuestion,
        conversation: mappedResponses,
        interviewerComments: comments,
        videoRecordings: separatedVideos,
      },
      userProfile: response,
      user: userResponse,
    },
  });
};

export default handler;
