import { db } from '@/prisma/db';

async function getUnansweredQuestions(careerPractice) {
  const unansweredVideoQuestions = careerPractice?.conversation
    .filter((item) => item.round === 'video-interview' && !item.isAnswered)
    .slice(0, 1); // Return only the first unanswered video question

  const unansweredCodingQuestions = careerPractice?.conversation.filter(
    (item) => item.round === 'coding-interview' && !item.isAnswered,
  );

  const unansweredFrontendQuestions = careerPractice?.conversation.filter(
    (item) => item.round === 'frontend-interview' && !item.isAnswered,
  );
  const unansweredMultipleChoiceQuestions = careerPractice?.conversation.filter(
    (item) => item.round === 'multiple-choice' && !item.isAnswered,
  );
  const unansweredWrittenQuestions = careerPractice?.conversation.filter(
    (item) => item.round === 'written-interview' && !item.isAnswered,
  );

  if (unansweredVideoQuestions?.length > 0) {
    if (!unansweredVideoQuestions[0].startTime) {
      // Attach startTime to the first unanswered question
      unansweredVideoQuestions[0].startTime = new Date(); // Set startTime to the current time

      // Save the updated careerPractice back to the database
      await db.careerPractice.update({
        where: { id: careerPractice.id },
        data: {
          conversation: careerPractice.conversation,
        },
      });

      // Return the first unanswered question with the startTime attached
      return unansweredVideoQuestions?.[0];
    }
    return unansweredVideoQuestions?.[0];
  }

  if (unansweredCodingQuestions?.length > 0) {
    return unansweredCodingQuestions;
  }

  if (unansweredFrontendQuestions?.length > 0) {
    return unansweredFrontendQuestions;
  }

  if (unansweredMultipleChoiceQuestions?.length > 0) {
    return unansweredMultipleChoiceQuestions;
  }
  if (unansweredWrittenQuestions?.length > 0) {
    return unansweredWrittenQuestions;
  }

  // No unanswered questions found
  return null;
}

const handler = async (req: any, res: any) => {
  const { id, userId } = (await req.body) as {
    id?: string;
    userId?: string;
  };

  if (!id) {
    return res.status(500).json({ error: 'Missing testId' });
  }

  if (!userId) {
    return res.status(500).json({ error: 'UserId Missing' });
  }

  try {
    const careerPractice = await db.careerPractice.findFirst({
      where: {
        id: id,
        userId: userId,
      },
      include: {
        eventDetails: {
          select: {
            name: true,
            timing: true,
            isAiQuestion: true,
            aiQuestionCount: true,
            organizationId: true,
            isProctor: true,
            isPlacement: true,
            jobDescription: true,
            customTopics: true,
            round:true,
          },
        },
      },
    });

    if (!careerPractice) {
      return res.status(400).json({ error: 'Invalid id' });
    }

    // Check if interview is already completed (for AI interviews and others)
    if (careerPractice.interviewStatus === 'COMPLETED') {
      return res.status(200).json({
        status: 'success',
        hasCompleted: true,
        isPlacement: careerPractice?.eventDetails?.isPlacement,
      });
    }
    if (!(careerPractice as any)?.timing?.startTime) {
      const codingQuestionLength = (careerPractice as any)?.conversation.filter(
        (item) => item.round === 'coding-interview',
      )?.length;
      const frontendQuestionLength = (careerPractice as any)?.conversation.filter(
        (item) => item.round === 'frontend-interview',
      )?.length;

      let previous_conversation = (careerPractice as any)?.conversation?.map((item) => {
        if (item?.answer.trim()?.length === 0) return null;
        return {
          turns: [
            { parts: [{ text: item?.question }], role: 'model' },
            { parts: [{ text: item?.answer }], role: 'user' },
          ],
          turnComplete: true,
        };
      });
      previous_conversation = previous_conversation.filter((item) => item !== null);
      const videoQuestionLength = (careerPractice as any)?.eventDetails?.isAiQuestion
        ? (careerPractice as any)?.eventDetails?.aiQuestionCount || 5
        : (careerPractice as any)?.conversation.filter((item) => item.round === 'video-interview')
            ?.length;

      return res.status(200).json({
        event: careerPractice?.eventDetails?.name,
        isAiQuestion: careerPractice?.eventDetails?.isAiQuestion,
        organizationId: careerPractice?.eventDetails?.organizationId,
        jobDescription: careerPractice?.eventDetails?.jobDescription,
        customTopics: careerPractice?.eventDetails?.customTopics,
        interviewerQuestions: careerPractice?.interviewerQuestions,
        eventId: careerPractice?.eventId,
        timing: (careerPractice as any)?.timing,
        role: (careerPractice as any)?.role,
        level: (careerPractice as any)?.level,
        videoQuestionLength,
        codingQuestionLength,
        frontendQuestionLength,
        previous_conversation,
        proctorWarnings: (careerPractice as any)?.proctorWarnings,
        isProctor: careerPractice?.eventDetails?.isProctor,
      });
    }

    const unansweredQuestions = await getUnansweredQuestions(careerPractice);

    const totalUnansweredQuestions = (careerPractice as any)?.conversation.filter(
      (item) => item?.isAnswered,
    )?.length;

    if (!unansweredQuestions) {
      return res.status(200).json({
        status: 'success',
        hasCompleted: true,
        isPlacement: careerPractice?.eventDetails?.isPlacement,
      });
    }

    const uniqueRounds = Array.isArray(careerPractice?.conversation)
      ? Array.from(new Set(careerPractice?.conversation.map((question: any) => question.round)))
      : [];
    if (Array.isArray(unansweredQuestions)) {
      const currentRoundTotal = (careerPractice as any)?.conversation?.filter(
        (item) => item?.round?.toLowerCase() === unansweredQuestions[0]?.round,
      )?.length;

      let previous_conversation = (careerPractice as any)?.conversation?.map((item) => {
        console.log({ length: item?.answer.trim()?.length });

        if (item?.answer.trim()?.length === 0) return null;
        return {
          turns: [
            { parts: [{ text: item?.question }], role: 'model' },
            { parts: [{ text: item?.answer }], role: 'user' },
          ],
          turnComplete: true,
        };
      });
      previous_conversation = previous_conversation.filter((item) => item !== null);
      return res.status(200).json({
        round: unansweredQuestions[0]?.round,
        videoUrl: unansweredQuestions[0]?.videoUrl,
        questions: unansweredQuestions,
        event: careerPractice?.eventDetails?.name,
        eventId: careerPractice?.eventId,
        role: careerPractice?.role,
        level: careerPractice?.level,
        previous_conversation,
        questionStatus: `${Number(totalUnansweredQuestions) + 1}/${currentRoundTotal}`,
        timing: (careerPractice as any)?.timing,
        rounds: uniqueRounds,
        proctorWarnings: (careerPractice as any)?.proctorWarnings,
        isProctor: careerPractice?.eventDetails?.isProctor,
        isPlacement: careerPractice?.eventDetails?.isPlacement,
        jobDescription: careerPractice?.eventDetails?.jobDescription,
        customTopics: careerPractice?.eventDetails?.customTopics,
        interviewerQuestions: careerPractice?.interviewerQuestions,
        isAiQuestion: careerPractice?.eventDetails?.isAiQuestion,
        organizationId: careerPractice?.eventDetails?.organizationId,
         interview_round: careerPractice?.eventDetails?.round,
        llm_provider: careerPractice?.comments?.llm_provider || 'google',
        video_avatar: careerPractice?.comments?.video_avatar || false
      });
    }

    if (typeof unansweredQuestions === 'object') {
      const currentRoundTotal = (careerPractice as any)?.conversation?.filter(
        (item) =>
          item?.round?.toLowerCase() === unansweredQuestions?.round?.toString()?.toLowerCase(),
      )?.length;

      let previous_conversation = (careerPractice as any)?.conversation?.map((item) => {
        console.log({ length: item?.answer.trim()?.length });

        if (item?.answer.trim()?.length === 0) return null;
        return {
          turns: [
            { parts: [{ text: item?.question }], role: 'model' },
            { parts: [{ text: item?.answer }], role: 'user' },
          ],
          turnComplete: true,
        };
      });
      previous_conversation = previous_conversation.filter((item) => item !== null);

      return res.status(200).json({
        ...unansweredQuestions,
        questionId: unansweredQuestions?.id,
        event: careerPractice?.eventDetails?.name,
        eventId: careerPractice?.eventId,
        role: careerPractice?.role,
        level: careerPractice?.level,
        previous_conversation,
        questionStatus: `${Number(totalUnansweredQuestions) + 1}/${currentRoundTotal}`,
        timing: (careerPractice as any)?.timing,
        rounds: uniqueRounds,
        proctorWarnings: (careerPractice as any)?.proctorWarnings,
        isProctor: careerPractice?.eventDetails?.isProctor,
        aiQuestionCount: careerPractice?.eventDetails?.aiQuestionCount,
        isPlacement: careerPractice?.eventDetails?.isPlacement,
        isAiQuestion: careerPractice?.eventDetails?.isAiQuestion,
        jobDescription: careerPractice?.eventDetails?.jobDescription,
        customTopics: careerPractice?.eventDetails?.customTopics,
        interviewerQuestions: careerPractice?.interviewerQuestions,
        organizationId: careerPractice?.eventDetails?.organizationId,
        interview_round: careerPractice?.eventDetails?.round,
        llm_provider: careerPractice?.comments?.llm_provider || 'openai',
        video_avatar: careerPractice?.comments?.video_avatar || false
      });
    }
  } catch (error) {
    console.error('An error occurred:', error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

export default handler;
