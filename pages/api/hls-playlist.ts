import { NextApiRequest, NextApiResponse } from 'next';
import { getHLSPlaylistSignedUrl } from './gcp-bucket';

export const config = {
  api: {
    bodyParser: false, // Disable body parsing for consistency
  },
};

/**
 * API endpoint to generate presigned URLs for HLS playlist files (.m3u8)
 * stored in the interview-ai folder in GCP bucket
 * 
 * Usage: /api/hls-playlist?practiceId=<practiceId>&organizationId=<organizationId>
 */
const handler = async (request: NextApiRequest, response: NextApiResponse) => {
  // Add CORS headers
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return response.status(200).end();
  }

  // Only allow GET requests
  if (request.method !== 'GET') {
    return response.status(405).json({ error: 'Method not allowed' });
  }

  const {
    practiceId,
    organizationId,
  } = request.query as {
    practiceId?: string;
    organizationId?: string;
  };

  // Validate required parameters
  if (!practiceId) {
    return response.status(400).json({ 
      error: 'Missing required parameter: practiceId' 
    });
  }

  try {
    console.log('Generating HLS playlist signed URL for:', { practiceId, organizationId });

    // Debug: Check what bucket name will be used
    console.log('Environment bucket:', process.env.NEXT_PUBLIC_GCP_BUCKET);

    // Generate signed URL for the HLS playlist file
    const originalSignedUrl = await getHLSPlaylistSignedUrl(practiceId, organizationId);

    console.log('Generated signed URL:', originalSignedUrl.substring(0, 100) + '...');

    // Fetch the playlist content and modify it to use direct GCP signed URLs for .ts files
    try {
      const playlistResponse = await fetch(originalSignedUrl);
      if (!playlistResponse.ok) {
        throw new Error(`Failed to fetch playlist: ${playlistResponse.status}`);
      }

      let playlistContent = await playlistResponse.text();
      console.log('Original playlist content:', playlistContent);

      // Replace .ts file references with direct GCP signed URLs
      const lines = playlistContent.split('\n');
      const modifiedLines = await Promise.all(lines.map(async (line) => {
        if (line.endsWith('.ts')) {
          try {
            // Generate direct signed URL for the .ts file
            const tsFilePath = `interview-ai/${line}`;
            const tsSignedUrl = await getHLSPlaylistSignedUrl(line.replace('.ts', ''), organizationId, true);
            console.log('Rewriting .ts URL:', line, '→', 'Direct GCP signed URL');
            return tsSignedUrl;
          } catch (error) {
            console.error('Error generating signed URL for .ts file:', line, error);
            return line; // Keep original if signing fails
          }
        }
        return line;
      }));

      const modifiedPlaylist = modifiedLines.join('\n');
      console.log('Modified playlist with direct signed URLs');

      // Return the modified playlist content directly as M3U8
      response.setHeader('Content-Type', 'application/vnd.apple.mpegurl');
      response.setHeader('Access-Control-Allow-Origin', '*');
      response.setHeader('Cache-Control', 'no-cache');
      return response.status(200).send(modifiedPlaylist);

    } catch (fetchError) {
      console.error('Error fetching/modifying playlist:', fetchError);

      // Fallback to original signed URL
      return response.status(200).json({
        signedUrl: originalSignedUrl,
        practiceId,
        filePath: `interview-ai/${practiceId}.m3u8`,
        bucketUsed: process.env.NEXT_PUBLIC_GCP_BUCKET,
        note: 'Using original playlist due to modification error'
      });
    }

  } catch (error) {
    console.error('Error generating HLS playlist signed URL:', error);
    return response.status(500).json({
      error: 'Failed to generate signed URL for HLS playlist',
      details: error.message
    });
  }
};

export default handler;
